using Core.DataAccess.EntityFramework;
using DataAccess.Abstract;
using DataAccess.Concrete.EntityFramework;
using Entities.Concrete;
using Entities.DTOs;
using System.Collections.Generic;
using System.Linq;

namespace DataAccess.Concrete.EntityFramework
{
    public class EfMemberWorkoutProgramDal : EfEntityRepositoryBase<MemberWorkoutProgram, GymContext>, IMemberWorkoutProgramDal
    {
        public List<MemberWorkoutProgramListDto> GetCompanyAssignments(int companyId)
        {
            using (GymContext context = new GymContext())
            {
                var result = from mwp in context.MemberWorkoutPrograms
                             join m in context.Members on mwp.MemberID equals m.MemberID
                             join wpt in context.WorkoutProgramTemplates on mwp.WorkoutProgramTemplateID equals wpt.WorkoutProgramTemplateID
                             where mwp.CompanyID == companyId && mwp.IsActive == true
                             orderby mwp.AssignedDate descending
                             select new MemberWorkoutProgramListDto
                             {
                                 MemberWorkoutProgramID = mwp.MemberWorkoutProgramID,
                                 MemberID = mwp.MemberID,
                                 MemberName = m.Name,
                                 ProgramName = wpt.ProgramName,
                                 ExperienceLevel = wpt.ExperienceLevel,
                                 TargetGoal = wpt.TargetGoal,
                                 AssignedDate = mwp.AssignedDate,
                                 StartDate = mwp.StartDate,
                                 EndDate = mwp.EndDate,
                                 IsActive = mwp.IsActive
                             };

                return result.ToList();
            }
        }

        public List<MemberWorkoutProgramDto> GetMemberActivePrograms(int memberId)
        {
            using (GymContext context = new GymContext())
            {
                var result = from mwp in context.MemberWorkoutPrograms
                             join m in context.Members on mwp.MemberID equals m.MemberID
                             join wpt in context.WorkoutProgramTemplates on mwp.WorkoutProgramTemplateID equals wpt.WorkoutProgramTemplateID
                             where mwp.MemberID == memberId && mwp.IsActive == true
                             orderby mwp.StartDate descending
                             select new MemberWorkoutProgramDto
                             {
                                 MemberWorkoutProgramID = mwp.MemberWorkoutProgramID,
                                 MemberID = mwp.MemberID,
                                 MemberName = m.Name,
                                 MemberPhone = m.PhoneNumber,
                                 WorkoutProgramTemplateID = mwp.WorkoutProgramTemplateID,
                                 ProgramName = wpt.ProgramName,
                                 ProgramDescription = wpt.Description,
                                 ExperienceLevel = wpt.ExperienceLevel,
                                 TargetGoal = wpt.TargetGoal,
                                 CompanyID = mwp.CompanyID,
                                 AssignedDate = mwp.AssignedDate,
                                 StartDate = mwp.StartDate,
                                 EndDate = mwp.EndDate,
                                 Notes = mwp.Notes,
                                 IsActive = mwp.IsActive,
                                 CreationDate = mwp.CreationDate
                             };

                return result.ToList();
            }
        }

        public List<MemberWorkoutProgramHistoryDto> GetMemberProgramHistory(int memberId)
        {
            using (GymContext context = new GymContext())
            {
                var result = from mwp in context.MemberWorkoutPrograms
                             join wpt in context.WorkoutProgramTemplates on mwp.WorkoutProgramTemplateID equals wpt.WorkoutProgramTemplateID
                             where mwp.MemberID == memberId
                             orderby mwp.AssignedDate descending
                             select new MemberWorkoutProgramHistoryDto
                             {
                                 MemberWorkoutProgramID = mwp.MemberWorkoutProgramID,
                                 ProgramName = wpt.ProgramName,
                                 AssignedDate = mwp.AssignedDate,
                                 StartDate = mwp.StartDate,
                                 EndDate = mwp.EndDate,
                                 IsActive = mwp.IsActive,
                                 Notes = mwp.Notes
                             };

                return result.ToList();
            }
        }

        public List<MemberActiveWorkoutProgramDto> GetActiveWorkoutProgramsByUserId(int userId)
        {
            using (GymContext context = new GymContext())
            {
                var result = from mwp in context.MemberWorkoutPrograms
                             join m in context.Members on mwp.MemberID equals m.MemberID
                             join wpt in context.WorkoutProgramTemplates on mwp.WorkoutProgramTemplateID equals wpt.WorkoutProgramTemplateID
                             where m.UserID == userId && mwp.IsActive == true && m.IsActive == true
                             orderby mwp.StartDate descending
                             select new MemberActiveWorkoutProgramDto
                             {
                                 MemberWorkoutProgramID = mwp.MemberWorkoutProgramID,
                                 WorkoutProgramTemplateID = mwp.WorkoutProgramTemplateID,
                                 ProgramName = wpt.ProgramName,
                                 ProgramDescription = wpt.Description,
                                 ExperienceLevel = wpt.ExperienceLevel,
                                 TargetGoal = wpt.TargetGoal,
                                 StartDate = mwp.StartDate,
                                 EndDate = mwp.EndDate,
                                 Notes = mwp.Notes
                             };

                return result.ToList();
            }
        }

        public MemberWorkoutProgramDto GetAssignmentDetail(int assignmentId)
        {
            using (GymContext context = new GymContext())
            {
                var result = from mwp in context.MemberWorkoutPrograms
                             join m in context.Members on mwp.MemberID equals m.MemberID
                             join wpt in context.WorkoutProgramTemplates on mwp.WorkoutProgramTemplateID equals wpt.WorkoutProgramTemplateID
                             where mwp.MemberWorkoutProgramID == assignmentId
                             select new MemberWorkoutProgramDto
                             {
                                 MemberWorkoutProgramID = mwp.MemberWorkoutProgramID,
                                 MemberID = mwp.MemberID,
                                 MemberName = m.Name,
                                 MemberPhone = m.PhoneNumber,
                                 WorkoutProgramTemplateID = mwp.WorkoutProgramTemplateID,
                                 ProgramName = wpt.ProgramName,
                                 ProgramDescription = wpt.Description,
                                 ExperienceLevel = wpt.ExperienceLevel,
                                 TargetGoal = wpt.TargetGoal,
                                 CompanyID = mwp.CompanyID,
                                 AssignedDate = mwp.AssignedDate,
                                 StartDate = mwp.StartDate,
                                 EndDate = mwp.EndDate,
                                 Notes = mwp.Notes,
                                 IsActive = mwp.IsActive,
                                 CreationDate = mwp.CreationDate
                             };

                return result.FirstOrDefault();
            }
        }

        public int GetAssignedMemberCount(int workoutProgramTemplateId)
        {
            using (GymContext context = new GymContext())
            {
                return context.MemberWorkoutPrograms
                    .Where(mwp => mwp.WorkoutProgramTemplateID == workoutProgramTemplateId && mwp.IsActive == true)
                    .Count();
            }
        }

        public int GetActiveAssignmentCount(int companyId)
        {
            using (GymContext context = new GymContext())
            {
                return context.MemberWorkoutPrograms
                    .Where(mwp => mwp.CompanyID == companyId && mwp.IsActive == true)
                    .Count();
            }
        }

        public MobileWorkoutProgramWithProgressDto GetProgramWithProgressForUser(int userId, int programId)
        {
            using (GymContext context = new GymContext())
            {
                // Ana program bilgilerini al
                var programInfo = (from mwp in context.MemberWorkoutPrograms
                                  join m in context.Members on mwp.MemberID equals m.MemberID
                                  join wpt in context.WorkoutProgramTemplates on mwp.WorkoutProgramTemplateID equals wpt.WorkoutProgramTemplateID
                                  where m.UserID == userId &&
                                        mwp.WorkoutProgramTemplateID == programId &&
                                        mwp.IsActive == true &&
                                        m.IsActive == true
                                  select new
                                  {
                                      wpt.WorkoutProgramTemplateID,
                                      wpt.ProgramName,
                                      wpt.Description,
                                      wpt.ExperienceLevel,
                                      wpt.TargetGoal,
                                      mwp.CurrentCycle,
                                      mwp.TotalCompletions,
                                      mwp.LastCompletedDate,
                                      m.MemberID
                                  }).FirstOrDefault();

                if (programInfo == null)
                {
                    return null;
                }

                // Günleri ve egzersizleri al
                var days = (from wpd in context.WorkoutProgramDays
                           where wpd.WorkoutProgramTemplateID == programId && wpd.DeletedDate == null
                           orderby wpd.DayNumber
                           select new MobileWorkoutDayWithProgressDto
                           {
                               WorkoutProgramDayID = wpd.WorkoutProgramDayID,
                               DayNumber = wpd.DayNumber,
                               DayName = wpd.DayName,
                               IsRestDay = wpd.IsRestDay ?? false,
                               Exercises = (from wpe in context.WorkoutProgramExercises
                                           join se in context.SystemExercises on wpe.SystemExerciseID equals se.SystemExerciseID into seGroup
                                           from se in seGroup.DefaultIfEmpty()
                                           join ce in context.CompanyExercises on wpe.CompanyExerciseID equals ce.CompanyExerciseID into ceGroup
                                           from ce in ceGroup.DefaultIfEmpty()
                                           where wpe.WorkoutProgramDayID == wpd.WorkoutProgramDayID && wpe.DeletedDate == null
                                           orderby wpe.OrderIndex
                                           select new MobileWorkoutExerciseWithProgressDto
                                           {
                                               WorkoutProgramExerciseID = wpe.WorkoutProgramExerciseID,
                                               ExerciseName = se != null ? se.ExerciseName : (ce != null ? ce.ExerciseName : "Bilinmeyen Egzersiz"),
                                               Sets = wpe.Sets,
                                               Repetitions = wpe.Repetitions,
                                               Notes = wpe.Notes,
                                               OrderIndex = wpe.OrderIndex,
                                               IsCompletedToday = context.MemberWorkoutProgresses.Any(p =>
                                                   p.MemberID == programInfo.MemberID &&
                                                   p.WorkoutProgramExerciseID == wpe.WorkoutProgramExerciseID &&
                                                   p.CycleNumber == programInfo.CurrentCycle &&
                                                   p.CompletedDate == DateTime.Today &&
                                                   p.IsCompleted == true &&
                                                   p.DeletedDate == null),
                                               LastCompletedDate = context.MemberWorkoutProgresses
                                                   .Where(p => p.MemberID == programInfo.MemberID &&
                                                              p.WorkoutProgramExerciseID == wpe.WorkoutProgramExerciseID &&
                                                              p.IsCompleted == true &&
                                                              p.DeletedDate == null)
                                                   .OrderByDescending(p => p.CompletedDate)
                                                   .Select(p => (DateTime?)p.CompletedDate)
                                                   .FirstOrDefault(),
                                               CompletedSets = context.MemberWorkoutProgresses
                                                   .Where(p => p.MemberID == programInfo.MemberID &&
                                                              p.WorkoutProgramExerciseID == wpe.WorkoutProgramExerciseID &&
                                                              p.CycleNumber == programInfo.CurrentCycle &&
                                                              p.CompletedDate == DateTime.Today &&
                                                              p.IsCompleted == true &&
                                                              p.DeletedDate == null)
                                                   .Select(p => p.CompletedSets)
                                                   .FirstOrDefault(),
                                               UserNotes = context.MemberWorkoutProgresses
                                                   .Where(p => p.MemberID == programInfo.MemberID &&
                                                              p.WorkoutProgramExerciseID == wpe.WorkoutProgramExerciseID &&
                                                              p.CycleNumber == programInfo.CurrentCycle &&
                                                              p.CompletedDate == DateTime.Today &&
                                                              p.IsCompleted == true &&
                                                              p.DeletedDate == null)
                                                   .Select(p => p.Notes)
                                                   .FirstOrDefault()
                                           }).ToList()
                           }).ToList();

                // Her gün için tamamlanan egzersiz sayısını hesapla
                foreach (var day in days)
                {
                    day.CompletedExercisesInDay = day.Exercises.Count(e => e.IsCompletedToday);
                    day.TotalExercisesInDay = day.Exercises.Count;
                }

                // Toplam istatistikleri hesapla
                var totalExercises = days.SelectMany(d => d.Exercises).Count();
                var completedExercises = days.SelectMany(d => d.Exercises).Count(e => e.IsCompletedToday);
                var progressPercentage = totalExercises > 0 ? (decimal)completedExercises / totalExercises * 100 : 0;

                return new MobileWorkoutProgramWithProgressDto
                {
                    WorkoutProgramTemplateID = programInfo.WorkoutProgramTemplateID,
                    ProgramName = programInfo.ProgramName,
                    Description = programInfo.Description,
                    ExperienceLevel = programInfo.ExperienceLevel,
                    TargetGoal = programInfo.TargetGoal,
                    CurrentCycle = programInfo.CurrentCycle,
                    TotalCompletions = programInfo.TotalCompletions,
                    LastCompletedDate = programInfo.LastCompletedDate,
                    ProgressPercentage = progressPercentage,
                    CompletedExercises = completedExercises,
                    TotalExercises = totalExercises,
                    Days = days
                };
            }
        }
    }
}
