using Core.Entities;
using System;
using System.ComponentModel.DataAnnotations;

namespace Entities.Concrete
{
    public class MemberWorkoutProgress : ICompanyEntity
    {
        [Key]
        public int MemberWorkoutProgressID { get; set; }
        public int MemberID { get; set; }
        public int WorkoutProgramTemplateID { get; set; }
        public int WorkoutProgramDayID { get; set; }
        public int WorkoutProgramExerciseID { get; set; }
        public int CompanyID { get; set; }
        public DateTime CompletedDate { get; set; } // Sad<PERSON>e tarih (gün bazlı)
        public DateTime CompletedDateTime { get; set; } // Tam zaman damgası
        public int CycleNumber { get; set; } = 1; // Hangi döngüde tamamlandı
        public int? CompletedSets { get; set; } // Tamamlanan set sayısı
        public string? Notes { get; set; } // Kullanıcı notları
        public bool IsCompleted { get; set; } = true; // Tamamlandı mı?
        public DateTime? CreationDate { get; set; }
        public DateTime? DeletedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
    }
}
