-- =============================================
-- <PERSON><PERSON>lerleme Sistemi Migration
-- Tarih: 2024
-- Açıklama: Otomatik restart sistemi ile ilerleme takibi
-- =============================================

-- 1. MemberWorkoutPrograms tablosuna yeni alanlar ekleme
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[MemberWorkoutPrograms]') AND name = 'CurrentCycle')
BEGIN
    ALTER TABLE [dbo].[MemberWorkoutPrograms] 
    ADD [CurrentCycle] [int] NOT NULL DEFAULT(1);
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[MemberWorkoutPrograms]') AND name = 'TotalCompletions')
BEGIN
    ALTER TABLE [dbo].[MemberWorkoutPrograms] 
    ADD [TotalCompletions] [int] NOT NULL DEFAULT(0);
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'[dbo].[MemberWorkoutPrograms]') AND name = 'LastCompletedDate')
BEGIN
    ALTER TABLE [dbo].[MemberWorkoutPrograms] 
    ADD [LastCompletedDate] [datetime2](7) NULL;
END

-- 2. MemberWorkoutProgress Tablosu (Yeni Tablo)
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[MemberWorkoutProgresses]') AND type in (N'U'))
BEGIN
    CREATE TABLE [dbo].[MemberWorkoutProgresses](
        [MemberWorkoutProgressID] [int] IDENTITY(1,1) NOT NULL,
        [MemberID] [int] NOT NULL,
        [WorkoutProgramTemplateID] [int] NOT NULL,
        [WorkoutProgramDayID] [int] NOT NULL,
        [WorkoutProgramExerciseID] [int] NOT NULL,
        [CompanyID] [int] NOT NULL,
        [CompletedDate] [date] NOT NULL, -- Sadece tarih (gün bazlı)
        [CompletedDateTime] [datetime2](7) NOT NULL, -- Tam zaman damgası
        [CycleNumber] [int] NOT NULL DEFAULT(1), -- Hangi döngüde tamamlandı
        [CompletedSets] [int] NULL, -- Tamamlanan set sayısı
        [Notes] [nvarchar](500) NULL, -- Kullanıcı notları
        [IsCompleted] [bit] NOT NULL DEFAULT(1), -- Tamamlandı mı?
        [CreationDate] [datetime2](7) NULL DEFAULT(GETDATE()),
        [DeletedDate] [datetime2](7) NULL,
        [UpdatedDate] [datetime2](7) NULL,
        
        CONSTRAINT [PK_MemberWorkoutProgresses] PRIMARY KEY CLUSTERED ([MemberWorkoutProgressID] ASC),
        
        -- Foreign Key Constraints
        CONSTRAINT [FK_MemberWorkoutProgresses_Members] FOREIGN KEY([MemberID]) 
            REFERENCES [dbo].[Members] ([MemberID]),
        CONSTRAINT [FK_MemberWorkoutProgresses_WorkoutProgramTemplates] FOREIGN KEY([WorkoutProgramTemplateID]) 
            REFERENCES [dbo].[WorkoutProgramTemplates] ([WorkoutProgramTemplateID]),
        CONSTRAINT [FK_MemberWorkoutProgresses_WorkoutProgramDays] FOREIGN KEY([WorkoutProgramDayID]) 
            REFERENCES [dbo].[WorkoutProgramDays] ([WorkoutProgramDayID]),
        CONSTRAINT [FK_MemberWorkoutProgresses_WorkoutProgramExercises] FOREIGN KEY([WorkoutProgramExerciseID]) 
            REFERENCES [dbo].[WorkoutProgramExercises] ([WorkoutProgramExerciseID]),
        CONSTRAINT [FK_MemberWorkoutProgresses_Companies] FOREIGN KEY([CompanyID]) 
            REFERENCES [dbo].[Companies] ([CompanyID])
    );
END

-- 3. Performance İndeksleri
-- Üye ve döngü bazlı sorgular için
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[MemberWorkoutProgresses]') AND name = N'IX_MemberWorkoutProgresses_MemberCycle')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_MemberWorkoutProgresses_MemberCycle] 
    ON [dbo].[MemberWorkoutProgresses] ([MemberID], [CycleNumber], [CompletedDate])
    INCLUDE ([WorkoutProgramExerciseID], [IsCompleted]);
END

-- Program bazlı sorgular için
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[MemberWorkoutProgresses]') AND name = N'IX_MemberWorkoutProgresses_Program')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_MemberWorkoutProgresses_Program] 
    ON [dbo].[MemberWorkoutProgresses] ([WorkoutProgramTemplateID], [CompanyID])
    INCLUDE ([MemberID], [CompletedDate], [IsCompleted]);
END

-- Günlük ilerleme sorguları için
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[MemberWorkoutProgresses]') AND name = N'IX_MemberWorkoutProgresses_Daily')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_MemberWorkoutProgresses_Daily] 
    ON [dbo].[MemberWorkoutProgresses] ([CompletedDate], [MemberID], [CycleNumber])
    INCLUDE ([WorkoutProgramExerciseID], [IsCompleted]);
END

-- Egzersiz bazlı sorgular için
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[MemberWorkoutProgresses]') AND name = N'IX_MemberWorkoutProgresses_Exercise')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_MemberWorkoutProgresses_Exercise] 
    ON [dbo].[MemberWorkoutProgresses] ([WorkoutProgramExerciseID], [MemberID])
    INCLUDE ([CompletedDate], [CycleNumber], [IsCompleted]);
END

-- Şirket bazlı sorgular için
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[MemberWorkoutProgresses]') AND name = N'IX_MemberWorkoutProgresses_Company')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_MemberWorkoutProgresses_Company] 
    ON [dbo].[MemberWorkoutProgresses] ([CompanyID], [DeletedDate])
    INCLUDE ([MemberID], [CompletedDate], [IsCompleted]);
END

-- 4. Unique Constraint - Aynı gün aynı egzersiz sadece 1 kez işaretlenebilir
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[MemberWorkoutProgresses]') AND name = N'UK_MemberWorkoutProgresses_Daily')
BEGIN
    CREATE UNIQUE NONCLUSTERED INDEX [UK_MemberWorkoutProgresses_Daily] 
    ON [dbo].[MemberWorkoutProgresses] ([MemberID], [WorkoutProgramExerciseID], [CompletedDate], [CycleNumber])
    WHERE [DeletedDate] IS NULL;
END

-- 5. MemberWorkoutPrograms tablosuna indeks ekleme
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID(N'[dbo].[MemberWorkoutPrograms]') AND name = N'IX_MemberWorkoutPrograms_CurrentCycle')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_MemberWorkoutPrograms_CurrentCycle] 
    ON [dbo].[MemberWorkoutPrograms] ([MemberID], [CurrentCycle], [IsActive])
    INCLUDE ([WorkoutProgramTemplateID], [TotalCompletions], [LastCompletedDate]);
END

PRINT 'MemberWorkoutProgress Migration tamamlandı.';
PRINT 'Otomatik restart sistemi ile ilerleme takibi aktif.';
PRINT 'Performance indeksleri eklendi.';
