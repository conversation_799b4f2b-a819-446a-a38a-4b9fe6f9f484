using Core.DataAccess;
using Entities.Concrete;
using Entities.DTOs;
using System;
using System.Collections.Generic;

namespace DataAccess.Abstract
{
    public interface IMemberWorkoutProgressDal : IEntityRepository<MemberWorkoutProgress>
    {
        /// <summary>
        /// Belirli üyenin belirli döngüdeki ilerleme kayıtlarını getirir
        /// </summary>
        List<MemberWorkoutProgress> GetProgressByCycle(int memberId, int programId, int cycleNumber);

        /// <summary>
        /// Belirli üyenin bugünkü ilerleme durumunu getirir
        /// </summary>
        List<MemberWorkoutProgress> GetTodayProgress(int memberId, int programId, int cycleNumber);

        /// <summary>
        /// Belirli üyenin toplam tamamladığı egzersiz sayısını getirir (mevcut döngü için)
        /// </summary>
        int GetCompletedExerciseCount(int memberId, int programId, int cycleNumber);

        /// <summary>
        /// Belirli egzersizin bugün tamamlanıp tamamlanmadığını kontrol eder
        /// </summary>
        bool IsExerciseCompletedToday(int memberId, int exerciseId, int cycleNumber);

        /// <summary>
        /// Belirli üyenin program istatistiklerini getirir
        /// </summary>
        MemberWorkoutProgressStatsDto GetProgressStats(int memberId, int programId);
    }
}
