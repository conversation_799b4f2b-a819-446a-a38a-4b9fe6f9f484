using Core.Utilities.Results;
using Entities.DTOs;
using System.Collections.Generic;

namespace Business.Abstract
{
    public interface IMemberWorkoutProgramService
    {
        /// <summary>
        /// Üyeye program atar
        /// </summary>
        IResult AssignProgram(MemberWorkoutProgramAddDto assignmentDto);

        /// <summary>
        /// Program atamasını günceller
        /// </summary>
        IResult UpdateAssignment(MemberWorkoutProgramUpdateDto assignmentDto);

        /// <summary>
        /// Program atamasını siler (soft delete)
        /// </summary>
        IResult DeleteAssignment(int assignmentId);

        /// <summary>
        /// Şirket bazlı tüm program atamalarını getirir
        /// </summary>
        IDataResult<List<MemberWorkoutProgramListDto>> GetCompanyAssignments();

        /// <summary>
        /// Belirli üyenin aktif programlarını getirir
        /// </summary>
        IDataResult<List<MemberWorkoutProgramDto>> GetMemberActivePrograms(int memberId);

        /// <summary>
        /// Belirli üyenin program geçmişini getirir
        /// </summary>
        IDataResult<List<MemberWorkoutProgramHistoryDto>> GetMemberProgramHistory(int memberId);

        /// <summary>
        /// User ID'ye göre aktif programları getirir (mobil API için)
        /// </summary>
        IDataResult<List<MemberActiveWorkoutProgramDto>> GetActiveWorkoutProgramsByUserId(int userId);

        /// <summary>
        /// Program atama detayını getirir
        /// </summary>
        IDataResult<MemberWorkoutProgramDto> GetAssignmentDetail(int assignmentId);

        /// <summary>
        /// Belirli programa atanan üye sayısını getirir
        /// </summary>
        IDataResult<int> GetAssignedMemberCount(int workoutProgramTemplateId);

        /// <summary>
        /// Şirket bazlı aktif atama sayısını getirir
        /// </summary>
        IDataResult<int> GetActiveAssignmentCount();
    }
}
