 using Core.Entities;
using System;

namespace Entities.DTOs
{
    public class MemberWorkoutProgramDto : IDto
    {
        public int MemberWorkoutProgramID { get; set; }
        public int MemberID { get; set; }
        public string MemberName { get; set; }
        public string? MemberPhone { get; set; }
        public int WorkoutProgramTemplateID { get; set; }
        public string ProgramName { get; set; }
        public string? ProgramDescription { get; set; }
        public string? ExperienceLevel { get; set; }
        public string? TargetGoal { get; set; }
        public int CompanyID { get; set; }
        public DateTime AssignedDate { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? Notes { get; set; }
        public bool IsActive { get; set; }
        public DateTime? CreationDate { get; set; }
        public int DayCount { get; set; } // Program gün sayısı
        public int ExerciseCount { get; set; } // Program egzersiz sayısı
    }

    public class MemberWorkoutProgramAddDto : IDto
    {
        public int MemberID { get; set; }
        public int WorkoutProgramTemplateID { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? Notes { get; set; }
    }

    public class MemberWorkoutProgramUpdateDto : IDto
    {
        public int MemberWorkoutProgramID { get; set; }
        public int WorkoutProgramTemplateID { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? Notes { get; set; }
        public bool IsActive { get; set; }
    }

    public class MemberWorkoutProgramListDto : IDto
    {
        public int MemberWorkoutProgramID { get; set; }
        public int MemberID { get; set; }
        public string MemberName { get; set; }
        public string ProgramName { get; set; }
        public string? ExperienceLevel { get; set; }
        public string? TargetGoal { get; set; }
        public DateTime AssignedDate { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public bool IsActive { get; set; }
    }

    // Mobil API için özel DTO
    public class MemberActiveWorkoutProgramDto : IDto
    {
        public int MemberWorkoutProgramID { get; set; }
        public int WorkoutProgramTemplateID { get; set; }
        public string ProgramName { get; set; }
        public string? ProgramDescription { get; set; }
        public string? ExperienceLevel { get; set; }
        public string? TargetGoal { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string? Notes { get; set; }
        public int DayCount { get; set; }
        public int ExerciseCount { get; set; }
        public int CurrentCycle { get; set; }
        public int TotalCompletions { get; set; }
        public DateTime? LastCompletedDate { get; set; }
        public decimal ProgressPercentage { get; set; }
        public int CompletedExercises { get; set; }
        public int TotalExercises { get; set; }
        // Program detayları (günler ve egzersizler) ayrı endpoint'ten gelecek
    }

    // Mobil program detayı için DTO
    public class MobileWorkoutProgramWithProgressDto : IDto
    {
        public int WorkoutProgramTemplateID { get; set; }
        public string ProgramName { get; set; }
        public string? Description { get; set; }
        public string? ExperienceLevel { get; set; }
        public string? TargetGoal { get; set; }
        public int CurrentCycle { get; set; }
        public int TotalCompletions { get; set; }
        public DateTime? LastCompletedDate { get; set; }
        public decimal ProgressPercentage { get; set; }
        public int CompletedExercises { get; set; }
        public int TotalExercises { get; set; }
        public List<MobileWorkoutDayWithProgressDto> Days { get; set; } = new List<MobileWorkoutDayWithProgressDto>();
    }

    public class MobileWorkoutDayWithProgressDto : IDto
    {
        public int WorkoutProgramDayID { get; set; }
        public int DayNumber { get; set; }
        public string DayName { get; set; }
        public bool IsRestDay { get; set; }
        public int CompletedExercisesInDay { get; set; }
        public int TotalExercisesInDay { get; set; }
        public List<MobileWorkoutExerciseWithProgressDto> Exercises { get; set; } = new List<MobileWorkoutExerciseWithProgressDto>();
    }

    public class MobileWorkoutExerciseWithProgressDto : IDto
    {
        public int WorkoutProgramExerciseID { get; set; }
        public string ExerciseName { get; set; }
        public int Sets { get; set; }
        public string Repetitions { get; set; }
        public string? Notes { get; set; }
        public int OrderIndex { get; set; }
        public bool IsCompletedToday { get; set; }
        public DateTime? LastCompletedDate { get; set; }
        public int? CompletedSets { get; set; }
        public string? UserNotes { get; set; }
    }

    // Egzersiz tamamlama için DTO
    public class ExerciseProgressDto : IDto
    {
        public int WorkoutProgramExerciseID { get; set; }
        public int? CompletedSets { get; set; }
        public string? Notes { get; set; }
    }

    // İlerleme istatistikleri için DTO
    public class MemberWorkoutProgressStatsDto : IDto
    {
        public int CurrentCycle { get; set; }
        public int TotalCompletions { get; set; }
        public DateTime? LastCompletedDate { get; set; }
        public int TotalExercises { get; set; }
        public int CompletedExercises { get; set; }
        public decimal ProgressPercentage { get; set; }
    }

    // Atama geçmişi için basit DTO
    public class MemberWorkoutProgramHistoryDto : IDto
    {
        public int MemberWorkoutProgramID { get; set; }
        public string ProgramName { get; set; }
        public DateTime AssignedDate { get; set; }
        public DateTime StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public bool IsActive { get; set; }
        public string? Notes { get; set; }
    }
}
