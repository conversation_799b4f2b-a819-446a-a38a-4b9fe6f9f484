using Core.Entities;
using System;
using System.ComponentModel.DataAnnotations;

namespace Entities.Concrete
{
    public class WorkoutProgramExercise : IEntity
    {
        [Key]
        public int WorkoutProgramExerciseID { get; set; }
        public int WorkoutProgramDayID { get; set; }
        public string ExerciseType { get; set; } // "System" veya "Company"
        public int ExerciseID { get; set; } // SystemExerciseID veya CompanyExerciseID
        public int? SystemExerciseID { get; set; } // Sistem egzersizi ID'si
        public int? CompanyExerciseID { get; set; } // Şirket egzersizi ID'si
        public int OrderIndex { get; set; } // Egzersiz sırası (1, 2, 3...)
        public int Sets { get; set; } // Set sayısı
        public string Repetitions { get; set; } // Tekrar sayısı (12, MAX, 12-15 vb.) - Reps yerine Repetitions
        public int? RestTime { get; set; } // Dinlenme süresi (saniye)
        public string? Notes { get; set; } // Egzersiz notları
        public DateTime? CreationDate { get; set; }
        public DateTime? DeletedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }

        // Navigation property
        public virtual WorkoutProgramDay WorkoutProgramDay { get; set; }
    }
}
