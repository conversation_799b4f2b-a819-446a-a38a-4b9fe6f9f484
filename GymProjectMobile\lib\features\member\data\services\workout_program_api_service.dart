/// Workout Program API Service - GymKod Pro Mobile
///
/// Bu service antrenman programı ile ilgili API çağrılarını yapar.
/// Backend'deki MemberWorkoutProgramController ile uyumlu.
library;

import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/core.dart';
import '../models/workout_models.dart';

/// Workout Program API Service
class WorkoutProgramApiService {
  final Dio _dio;
  final String _baseUrl;

  WorkoutProgramApiService(this._dio) : _baseUrl = '${ApiConstants.baseUrl}/memberworkoutprogram';

  /// User ID'ye göre aktif programları getirir (mobil API için)
  /// JWT token'dan user ID otomatik alınır
  Future<ListResponseModel<MemberActiveWorkoutProgram>> getActiveWorkoutProgramsByUser() async {
    try {
      LoggingService.apiLog('GET', '$_baseUrl/getactiveprogramsbyuser', null);
      
      final response = await _dio.get('$_baseUrl/getactiveprogramsbyuser');
      
      LoggingService.apiLog('RESPONSE', '$_baseUrl/getactiveprogramsbyuser', response.data);
      
      return ListResponseModel.fromJson(
        response.data,
        (json) => MemberActiveWorkoutProgram.fromJson(json as Map<String, dynamic>),
      );
    } on DioException catch (e) {
      LoggingService.error('API Error: ${e.message}', tag: 'WORKOUT_API');
      throw ApiException.fromDioError(e);
    } catch (e) {
      LoggingService.error('Unexpected error: $e', tag: 'WORKOUT_API');
      throw ApiException(message: 'Beklenmeyen bir hata oluştu: $e');
    }
  }

  /// Mobil için program detayını ilerleme ile birlikte getirir
  Future<SingleResponseModel<MobileWorkoutProgramWithProgress>> getProgramWithProgress(int programId) async {
    try {
      LoggingService.apiLog('GET', '$_baseUrl/getprogramwithprogress', {'programId': programId});
      
      final response = await _dio.get(
        '$_baseUrl/getprogramwithprogress',
        queryParameters: {'programId': programId},
      );
      
      LoggingService.apiLog('RESPONSE', '$_baseUrl/getprogramwithprogress', response.data);
      
      return SingleResponseModel.fromJson(
        response.data,
        (json) => MobileWorkoutProgramWithProgress.fromJson(json as Map<String, dynamic>),
      );
    } on DioException catch (e) {
      LoggingService.error('API Error: ${e.message}', tag: 'WORKOUT_API');
      throw ApiException.fromDioError(e);
    } catch (e) {
      LoggingService.error('Unexpected error: $e', tag: 'WORKOUT_API');
      throw ApiException(message: 'Beklenmeyen bir hata oluştu: $e');
    }
  }

  /// Egzersiz tamamlama işlemi (mobil API için)
  Future<ResponseModel> markExerciseComplete(ExerciseProgressRequest progressRequest) async {
    try {
      LoggingService.apiLog('POST', '$_baseUrl/markexercisecomplete', progressRequest.toJson());
      
      final response = await _dio.post(
        '$_baseUrl/markexercisecomplete',
        data: progressRequest.toJson(),
      );
      
      LoggingService.apiLog('RESPONSE', '$_baseUrl/markexercisecomplete', response.data);
      
      return ResponseModel.fromJson(response.data);
    } on DioException catch (e) {
      LoggingService.error('API Error: ${e.message}', tag: 'WORKOUT_API');
      throw ApiException.fromDioError(e);
    } catch (e) {
      LoggingService.error('Unexpected error: $e', tag: 'WORKOUT_API');
      throw ApiException(message: 'Beklenmeyen bir hata oluştu: $e');
    }
  }

  /// User ID'ye göre program istatistiklerini getirir
  Future<SingleResponseModel<MemberWorkoutProgressStats>> getProgressStats(int programId) async {
    try {
      LoggingService.apiLog('GET', '$_baseUrl/getprogressstats', {'programId': programId});
      
      final response = await _dio.get(
        '$_baseUrl/getprogressstats',
        queryParameters: {'programId': programId},
      );
      
      LoggingService.apiLog('RESPONSE', '$_baseUrl/getprogressstats', response.data);
      
      return SingleResponseModel.fromJson(
        response.data,
        (json) => MemberWorkoutProgressStats.fromJson(json as Map<String, dynamic>),
      );
    } on DioException catch (e) {
      LoggingService.error('API Error: ${e.message}', tag: 'WORKOUT_API');
      throw ApiException.fromDioError(e);
    } catch (e) {
      LoggingService.error('Unexpected error: $e', tag: 'WORKOUT_API');
      throw ApiException(message: 'Beklenmeyen bir hata oluştu: $e');
    }
  }
}

/// Provider for WorkoutProgramApiService
final workoutProgramApiServiceProvider = Provider<WorkoutProgramApiService>((ref) {
  final dio = ref.watch(dioProvider);
  return WorkoutProgramApiService(dio);
});
