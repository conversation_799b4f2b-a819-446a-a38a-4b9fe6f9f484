using Core.Entities;
using System;
using System.ComponentModel.DataAnnotations;

namespace Entities.Concrete
{
    public class MemberWorkoutProgram : ICompanyEntity
    {
        [Key]
        public int MemberWorkoutProgramID { get; set; }
        public int MemberID { get; set; }
        public int WorkoutProgramTemplateID { get; set; }
        public int CompanyID { get; set; }
        public DateTime AssignedDate { get; set; } // Atama tarihi
        public DateTime StartDate { get; set; } // Program başlangıç tarihi
        public DateTime? EndDate { get; set; } // Program bitiş tarihi (opsiyonel)
        public string? Notes { get; set; } // Atama notları
        public bool IsActive { get; set; } // Aktif mi?
        public int CurrentCycle { get; set; } = 1; // Mevcut döngü numarası
        public int TotalCompletions { get; set; } = 0; // Toplam tamamlama sayısı
        public DateTime? LastCompletedDate { get; set; } // Son tamamlama tarihi
        public DateTime? CreationDate { get; set; }
        public DateTime? DeletedDate { get; set; }
        public DateTime? UpdatedDate { get; set; }
    }
}
