/// Workout Program Page - GymKod Pro Mobile
///
/// Bu sayfa üyelerin antrenman programlarını görüntülemesi için oluşturulmuştur.
/// Angular frontend'deki tasarım sistemini takip eder.
///
/// RESPONSIVE DESIGN:
/// - Responsive card layout ve grid system
/// - Responsive typography scaling
/// - Responsive spacing ve padding
/// - Responsive icon sizes ve container dimensions
/// - Responsive workout card layout
library;

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/core.dart';
import '../../../../shared/widgets/responsive_builder.dart';

/// Workout Program Page
/// Member rolündeki kullanıcılar için antrenman programı sayfası
class WorkoutProgramPage extends ConsumerStatefulWidget {
  const WorkoutProgramPage({super.key});

  @override
  ConsumerState<WorkoutProgramPage> createState() => _WorkoutProgramPageState();
}

class _WorkoutProgramPageState extends ConsumerState<WorkoutProgramPage> {
  @override
  void initState() {
    super.initState();

    // Sayfa açıldığında antrenman programını yükle
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // TODO: Antrenman programı provider'ı eklendiğinde buraya gelecek
      LoggingService.info('Workout program page loaded', tag: 'WORKOUT');
    });
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return ResponsiveBuilder(
      builder: (context, deviceType) {
        return Scaffold(

          // Responsive Body
          body: ResponsiveContainer(
            child: Container(
              width: double.infinity,
              height: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    theme.colorScheme.primary,
                    theme.colorScheme.primary.withValues(alpha: 0.8),
                    theme.colorScheme.surface,
                  ],
                ),
              ),
              child: SafeArea(
                child: Column(
                  children: [
                    // Responsive Content Section
                    Expanded(
                      child: Padding(
                        padding: AppSpacing.responsiveScreenPadding(context),
                        child: _buildResponsiveWorkoutContent(theme, deviceType),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }



  /// Responsive Workout Content
  Widget _buildResponsiveWorkoutContent(ThemeData theme, DeviceType deviceType) {
    return SingleChildScrollView(
      child: Column(
        children: [
          ResponsiveSpacing.vertical(
            mobile: 16.0,
            tablet: 20.0,
            desktop: 24.0,
          ),

          // Responsive geçici içerik - İlerde gerçek antrenman programı gelecek
          _buildResponsiveTemporaryWorkoutContent(theme, deviceType),
        ],
      ),
    );
  }

  /// Responsive Geçici Antrenman İçeriği
  Widget _buildResponsiveTemporaryWorkoutContent(ThemeData theme, DeviceType deviceType) {
    return Column(
      children: [
        // Responsive Bilgi kartı
        ResponsiveCard(
          padding: AppSpacing.responsiveCardPadding(context),
          child: Column(
            children: [
              Icon(
                Icons.construction,
                size: AppSpacing.responsiveIconSize(context,
                  mobile: 40.0,
                  tablet: 48.0,
                  desktop: 56.0,
                ),
                color: theme.colorScheme.primary,
              ),
              ResponsiveSpacing.vertical(
                mobile: 12.0,
                tablet: 16.0,
                desktop: 20.0,
              ),
              ResponsiveText(
                'Antrenman Programı',
                textType: 'h3',
                style: const TextStyle(
                  fontWeight: FontWeight.bold,
                ),
              ),
              ResponsiveSpacing.vertical(
                mobile: 6.0,
                tablet: 8.0,
                desktop: 10.0,
              ),
              ResponsiveText(
                'Bu özellik yakında eklenecek. Kişisel antrenman programınızı burada görüntüleyebileceksiniz.',
                textType: 'bodymedium',
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),

        ResponsiveSpacing.vertical(
          mobile: 16.0,
          tablet: 20.0,
          desktop: 24.0,
        ),

        // Responsive örnek antrenman kartları
        _buildResponsiveSampleWorkoutCard(theme, deviceType, 'Pazartesi', 'Göğüs - Triceps', [
          'Bench Press: 4x8-10',
          'Incline Dumbbell Press: 3x10-12',
          'Dips: 3x12-15',
          'Triceps Pushdown: 3x12-15',
        ]),

        ResponsiveSpacing.vertical(
          mobile: 12.0,
          tablet: 16.0,
          desktop: 20.0,
        ),

        _buildResponsiveSampleWorkoutCard(theme, deviceType, 'Çarşamba', 'Sırt - Biceps', [
          'Pull-ups: 4x8-10',
          'Barbell Rows: 3x10-12',
          'Lat Pulldown: 3x12-15',
          'Bicep Curls: 3x12-15',
        ]),

        ResponsiveSpacing.vertical(
          mobile: 12.0,
          tablet: 16.0,
          desktop: 20.0,
        ),

        _buildResponsiveSampleWorkoutCard(theme, deviceType, 'Cuma', 'Bacak - Omuz', [
          'Squats: 4x8-10',
          'Romanian Deadlift: 3x10-12',
          'Shoulder Press: 3x10-12',
          'Lateral Raises: 3x12-15',
        ]),

        ResponsiveSpacing.vertical(
          mobile: 16.0,
          tablet: 20.0,
          desktop: 24.0,
        ),
      ],
    );
  }

  /// Responsive Örnek Antrenman Kartı
  Widget _buildResponsiveSampleWorkoutCard(ThemeData theme, DeviceType deviceType, String day, String title, List<String> exercises) {
    return ResponsiveCard(
      padding: AppSpacing.responsiveCardPadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Responsive Gün ve başlık
          Row(
            children: [
              Container(
                padding: EdgeInsets.symmetric(
                  horizontal: AppSpacing.responsive(context,
                    mobile: 10.0,
                    tablet: 12.0,
                    desktop: 14.0,
                  ),
                  vertical: AppSpacing.responsive(context,
                    mobile: 4.0,
                    tablet: 6.0,
                    desktop: 8.0,
                  ),
                ),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primary,
                  borderRadius: BorderRadius.circular(AppSpacing.responsiveBorderRadius(context,
                    mobile: 14.0,
                    tablet: 16.0,
                    desktop: 18.0,
                  )),
                ),
                child: ResponsiveText(
                  day,
                  textType: 'labelmedium',
                  style: TextStyle(
                    color: theme.colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              ResponsiveSpacing.horizontal(
                mobile: 10.0,
                tablet: 12.0,
                desktop: 14.0,
              ),
              Expanded(
                child: ResponsiveText(
                  title,
                  textType: 'cardtitle',
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),

          ResponsiveSpacing.vertical(
            mobile: 10.0,
            tablet: 12.0,
            desktop: 14.0,
          ),

          // Responsive Egzersizler
          ...exercises.map((exercise) => Padding(
            padding: EdgeInsets.symmetric(
              vertical: AppSpacing.responsive(context,
                mobile: 1.5,
                tablet: 2.0,
                desktop: 2.5,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.fitness_center,
                  size: AppSpacing.responsiveIconSize(context,
                    mobile: 14.0,
                    tablet: 16.0,
                    desktop: 18.0,
                  ),
                  color: theme.colorScheme.primary,
                ),
                ResponsiveSpacing.horizontal(
                  mobile: 6.0,
                  tablet: 8.0,
                  desktop: 10.0,
                ),
                Expanded(
                  child: ResponsiveText(
                    exercise,
                    textType: 'bodymedium',
                  ),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }
}
