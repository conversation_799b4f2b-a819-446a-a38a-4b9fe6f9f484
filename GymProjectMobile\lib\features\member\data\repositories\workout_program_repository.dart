/// Workout Program Repository - GymKod Pro Mobile
///
/// Bu repository antrenman programı verilerini yönetir.
/// Repository pattern ile API service'ini wrap eder.
library;

import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/core.dart';
import '../models/workout_models.dart';
import '../services/workout_program_api_service.dart';

/// Workout Program Repository Interface
abstract class WorkoutProgramRepository {
  Future<List<MemberActiveWorkoutProgram>> getActiveWorkoutPrograms();
  Future<MobileWorkoutProgramWithProgress> getProgramWithProgress(int programId);
  Future<void> markExerciseComplete(ExerciseProgressRequest progressRequest);
  Future<MemberWorkoutProgressStats> getProgressStats(int programId);
}

/// Workout Program Repository Implementation
class WorkoutProgramRepositoryImpl implements WorkoutProgramRepository {
  final WorkoutProgramApiService _apiService;

  WorkoutProgramRepositoryImpl(this._apiService);

  @override
  Future<List<MemberActiveWorkoutProgram>> getActiveWorkoutPrograms() async {
    try {
      LoggingService.info('Fetching active workout programs', tag: 'WORKOUT_REPO');
      
      final response = await _apiService.getActiveWorkoutProgramsByUser();
      
      if (response.success) {
        LoggingService.info('Successfully fetched ${response.data.length} programs', tag: 'WORKOUT_REPO');
        return response.data;
      } else {
        throw RepositoryException(message: response.message ?? 'Programlar alınamadı');
      }
    } on ApiException catch (e) {
      LoggingService.error('API Exception: ${e.message}', tag: 'WORKOUT_REPO');
      throw RepositoryException(message: e.message);
    } catch (e) {
      LoggingService.error('Repository error: $e', tag: 'WORKOUT_REPO');
      throw RepositoryException(message: 'Programlar alınırken hata oluştu: $e');
    }
  }

  @override
  Future<MobileWorkoutProgramWithProgress> getProgramWithProgress(int programId) async {
    try {
      LoggingService.info('Fetching program with progress: $programId', tag: 'WORKOUT_REPO');
      
      final response = await _apiService.getProgramWithProgress(programId);
      
      if (response.success) {
        LoggingService.info('Successfully fetched program with progress', tag: 'WORKOUT_REPO');
        return response.data;
      } else {
        throw RepositoryException(message: response.message ?? 'Program detayı alınamadı');
      }
    } on ApiException catch (e) {
      LoggingService.error('API Exception: ${e.message}', tag: 'WORKOUT_REPO');
      throw RepositoryException(message: e.message);
    } catch (e) {
      LoggingService.error('Repository error: $e', tag: 'WORKOUT_REPO');
      throw RepositoryException(message: 'Program detayı alınırken hata oluştu: $e');
    }
  }

  @override
  Future<void> markExerciseComplete(ExerciseProgressRequest progressRequest) async {
    try {
      LoggingService.info('Marking exercise complete: ${progressRequest.workoutProgramExerciseID}', tag: 'WORKOUT_REPO');
      
      final response = await _apiService.markExerciseComplete(progressRequest);
      
      if (response.success) {
        LoggingService.info('Successfully marked exercise complete', tag: 'WORKOUT_REPO');
      } else {
        throw RepositoryException(message: response.message ?? 'Egzersiz tamamlanamadı');
      }
    } on ApiException catch (e) {
      LoggingService.error('API Exception: ${e.message}', tag: 'WORKOUT_REPO');
      throw RepositoryException(message: e.message);
    } catch (e) {
      LoggingService.error('Repository error: $e', tag: 'WORKOUT_REPO');
      throw RepositoryException(message: 'Egzersiz tamamlanırken hata oluştu: $e');
    }
  }

  @override
  Future<MemberWorkoutProgressStats> getProgressStats(int programId) async {
    try {
      LoggingService.info('Fetching progress stats: $programId', tag: 'WORKOUT_REPO');
      
      final response = await _apiService.getProgressStats(programId);
      
      if (response.success) {
        LoggingService.info('Successfully fetched progress stats', tag: 'WORKOUT_REPO');
        return response.data;
      } else {
        throw RepositoryException(message: response.message ?? 'İstatistikler alınamadı');
      }
    } on ApiException catch (e) {
      LoggingService.error('API Exception: ${e.message}', tag: 'WORKOUT_REPO');
      throw RepositoryException(message: e.message);
    } catch (e) {
      LoggingService.error('Repository error: $e', tag: 'WORKOUT_REPO');
      throw RepositoryException(message: 'İstatistikler alınırken hata oluştu: $e');
    }
  }
}

/// Provider for WorkoutProgramRepository
final workoutProgramRepositoryProvider = Provider<WorkoutProgramRepository>((ref) {
  final apiService = ref.watch(workoutProgramApiServiceProvider);
  return WorkoutProgramRepositoryImpl(apiService);
});
