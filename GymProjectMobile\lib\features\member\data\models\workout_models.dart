/// Workout Program Models - GymKod Pro Mobile
///
/// Bu dosya antrenman programı ile ilgili model'ları içerir.
/// Backend'deki DTO'larla uyumlu olarak tasarlanmıştır.
library;

/// Mobil için aktif program listesi
class MemberActiveWorkoutProgram {
  final int memberWorkoutProgramID;
  final int workoutProgramTemplateID;
  final String programName;
  final String? programDescription;
  final String? experienceLevel;
  final String? targetGoal;
  final DateTime startDate;
  final DateTime? endDate;
  final String? notes;
  final int dayCount;
  final int exerciseCount;
  final int currentCycle;
  final int totalCompletions;
  final DateTime? lastCompletedDate;
  final double progressPercentage;
  final int completedExercises;
  final int totalExercises;

  const MemberActiveWorkoutProgram({
    required this.memberWorkoutProgramID,
    required this.workoutProgramTemplateID,
    required this.programName,
    this.programDescription,
    this.experienceLevel,
    this.targetGoal,
    required this.startDate,
    this.endDate,
    this.notes,
    required this.dayCount,
    required this.exerciseCount,
    required this.currentCycle,
    required this.totalCompletions,
    this.lastCompletedDate,
    required this.progressPercentage,
    required this.completedExercises,
    required this.totalExercises,
  });

  factory MemberActiveWorkoutProgram.fromJson(Map<String, dynamic> json) {
    return MemberActiveWorkoutProgram(
      memberWorkoutProgramID: json['memberWorkoutProgramID'] as int,
      workoutProgramTemplateID: json['workoutProgramTemplateID'] as int,
      programName: json['programName'] as String,
      programDescription: json['programDescription'] as String?,
      experienceLevel: json['experienceLevel'] as String?,
      targetGoal: json['targetGoal'] as String?,
      startDate: DateTime.parse(json['startDate'] as String),
      endDate: json['endDate'] != null ? DateTime.parse(json['endDate'] as String) : null,
      notes: json['notes'] as String?,
      dayCount: json['dayCount'] as int,
      exerciseCount: json['exerciseCount'] as int,
      currentCycle: json['currentCycle'] as int,
      totalCompletions: json['totalCompletions'] as int,
      lastCompletedDate: json['lastCompletedDate'] != null ? DateTime.parse(json['lastCompletedDate'] as String) : null,
      progressPercentage: (json['progressPercentage'] as num).toDouble(),
      completedExercises: json['completedExercises'] as int,
      totalExercises: json['totalExercises'] as int,
    );
  }
}

/// Mobil için program detayı (ilerleme ile birlikte)
class MobileWorkoutProgramWithProgress {
  final int workoutProgramTemplateID;
  final String programName;
  final String? description;
  final String? experienceLevel;
  final String? targetGoal;
  final int currentCycle;
  final int totalCompletions;
  final DateTime? lastCompletedDate;
  final double progressPercentage;
  final int completedExercises;
  final int totalExercises;
  final List<MobileWorkoutDayWithProgress> days;

  const MobileWorkoutProgramWithProgress({
    required this.workoutProgramTemplateID,
    required this.programName,
    this.description,
    this.experienceLevel,
    this.targetGoal,
    required this.currentCycle,
    required this.totalCompletions,
    this.lastCompletedDate,
    required this.progressPercentage,
    required this.completedExercises,
    required this.totalExercises,
    required this.days,
  });

  factory MobileWorkoutProgramWithProgress.fromJson(Map<String, dynamic> json) {
    return MobileWorkoutProgramWithProgress(
      workoutProgramTemplateID: json['workoutProgramTemplateID'] as int,
      programName: json['programName'] as String,
      description: json['description'] as String?,
      experienceLevel: json['experienceLevel'] as String?,
      targetGoal: json['targetGoal'] as String?,
      currentCycle: json['currentCycle'] as int,
      totalCompletions: json['totalCompletions'] as int,
      lastCompletedDate: json['lastCompletedDate'] != null ? DateTime.parse(json['lastCompletedDate'] as String) : null,
      progressPercentage: (json['progressPercentage'] as num).toDouble(),
      completedExercises: json['completedExercises'] as int,
      totalExercises: json['totalExercises'] as int,
      days: (json['days'] as List<dynamic>)
          .map((e) => MobileWorkoutDayWithProgress.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

/// Mobil için gün detayı (ilerleme ile birlikte)
class MobileWorkoutDayWithProgress {
  final int workoutProgramDayID;
  final int dayNumber;
  final String dayName;
  final bool isRestDay;
  final int completedExercisesInDay;
  final int totalExercisesInDay;
  final List<MobileWorkoutExerciseWithProgress> exercises;

  const MobileWorkoutDayWithProgress({
    required this.workoutProgramDayID,
    required this.dayNumber,
    required this.dayName,
    required this.isRestDay,
    required this.completedExercisesInDay,
    required this.totalExercisesInDay,
    required this.exercises,
  });

  factory MobileWorkoutDayWithProgress.fromJson(Map<String, dynamic> json) {
    return MobileWorkoutDayWithProgress(
      workoutProgramDayID: json['workoutProgramDayID'] as int,
      dayNumber: json['dayNumber'] as int,
      dayName: json['dayName'] as String,
      isRestDay: json['isRestDay'] as bool,
      completedExercisesInDay: json['completedExercisesInDay'] as int,
      totalExercisesInDay: json['totalExercisesInDay'] as int,
      exercises: (json['exercises'] as List<dynamic>)
          .map((e) => MobileWorkoutExerciseWithProgress.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

/// Mobil için egzersiz detayı (ilerleme ile birlikte)
class MobileWorkoutExerciseWithProgress {
  final int workoutProgramExerciseID;
  final String exerciseName;
  final int sets;
  final String repetitions;
  final String? notes;
  final int orderIndex;
  final bool isCompletedToday;
  final DateTime? lastCompletedDate;
  final int? completedSets;
  final String? userNotes;

  const MobileWorkoutExerciseWithProgress({
    required this.workoutProgramExerciseID,
    required this.exerciseName,
    required this.sets,
    required this.repetitions,
    this.notes,
    required this.orderIndex,
    required this.isCompletedToday,
    this.lastCompletedDate,
    this.completedSets,
    this.userNotes,
  });

  factory MobileWorkoutExerciseWithProgress.fromJson(Map<String, dynamic> json) {
    return MobileWorkoutExerciseWithProgress(
      workoutProgramExerciseID: json['workoutProgramExerciseID'] as int,
      exerciseName: json['exerciseName'] as String,
      sets: json['sets'] as int,
      repetitions: json['repetitions'] as String,
      notes: json['notes'] as String?,
      orderIndex: json['orderIndex'] as int,
      isCompletedToday: json['isCompletedToday'] as bool,
      lastCompletedDate: json['lastCompletedDate'] != null ? DateTime.parse(json['lastCompletedDate'] as String) : null,
      completedSets: json['completedSets'] as int?,
      userNotes: json['userNotes'] as String?,
    );
  }
}

/// Egzersiz tamamlama için DTO
class ExerciseProgressRequest {
  final int workoutProgramExerciseID;
  final int? completedSets;
  final String? notes;

  const ExerciseProgressRequest({
    required this.workoutProgramExerciseID,
    this.completedSets,
    this.notes,
  });

  Map<String, dynamic> toJson() {
    return {
      'workoutProgramExerciseID': workoutProgramExerciseID,
      'completedSets': completedSets,
      'notes': notes,
    };
  }
}

/// API Response Models
class ListResponseModel<T> {
  final List<T> data;
  final bool success;
  final String? message;

  const ListResponseModel({
    required this.data,
    required this.success,
    this.message,
  });

  factory ListResponseModel.fromJson(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    return ListResponseModel<T>(
      data: (json['data'] as List<dynamic>)
          .map((e) => fromJsonT(e as Map<String, dynamic>))
          .toList(),
      success: json['success'] as bool,
      message: json['message'] as String?,
    );
  }
}

class SingleResponseModel<T> {
  final T data;
  final bool success;
  final String? message;

  const SingleResponseModel({
    required this.data,
    required this.success,
    this.message,
  });

  factory SingleResponseModel.fromJson(
    Map<String, dynamic> json,
    T Function(Map<String, dynamic>) fromJsonT,
  ) {
    return SingleResponseModel<T>(
      data: fromJsonT(json['data'] as Map<String, dynamic>),
      success: json['success'] as bool,
      message: json['message'] as String?,
    );
  }
}

class ResponseModel {
  final bool success;
  final String? message;

  const ResponseModel({
    required this.success,
    this.message,
  });

  factory ResponseModel.fromJson(Map<String, dynamic> json) {
    return ResponseModel(
      success: json['success'] as bool,
      message: json['message'] as String?,
    );
  }
}

/// Workout Program State
class WorkoutProgramState {
  final List<MemberActiveWorkoutProgram> programs;
  final MobileWorkoutProgramWithProgress? selectedProgram;
  final bool isLoading;
  final bool isLoadingDetail;
  final bool isMarkingComplete;
  final String? error;
  final String? detailError;
  final Map<int, bool> exerciseCompletionStatus;

  const WorkoutProgramState({
    this.programs = const [],
    this.selectedProgram,
    this.isLoading = false,
    this.isLoadingDetail = false,
    this.isMarkingComplete = false,
    this.error,
    this.detailError,
    this.exerciseCompletionStatus = const {},
  });

  WorkoutProgramState copyWith({
    List<MemberActiveWorkoutProgram>? programs,
    MobileWorkoutProgramWithProgress? selectedProgram,
    bool? isLoading,
    bool? isLoadingDetail,
    bool? isMarkingComplete,
    String? error,
    String? detailError,
    Map<int, bool>? exerciseCompletionStatus,
  }) {
    return WorkoutProgramState(
      programs: programs ?? this.programs,
      selectedProgram: selectedProgram ?? this.selectedProgram,
      isLoading: isLoading ?? this.isLoading,
      isLoadingDetail: isLoadingDetail ?? this.isLoadingDetail,
      isMarkingComplete: isMarkingComplete ?? this.isMarkingComplete,
      error: error ?? this.error,
      detailError: detailError ?? this.detailError,
      exerciseCompletionStatus: exerciseCompletionStatus ?? this.exerciseCompletionStatus,
    );
  }
}
